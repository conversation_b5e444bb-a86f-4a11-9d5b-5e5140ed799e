import { useEffect, useRef, useState, useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import ai from "../../../public/assets/images/ai.png";
import planner from "../../../public/assets/images/planner.png";
import scheduler from "../../../public/assets/images/scheduler.png";
import forms from "../../../public/assets/images/forms.png";
import auto from "../../../public/assets/images/auto.png";
import analytics from "../../../public/assets/images/analytics.png";
import messaging from "../../../public/assets/images/messaging.png";
import referals from "../../../public/assets/images/referals.png";
import inventory from "../../../public/assets/images/inventory.png";
import assets from "../../../public/assets/images/assets.png";
import market from "../../../public/assets/images/market.png";
import { ArrowLeft, ArrowRight } from "lucide-react";

const emblaStyles = `
  .embla {
    overflow: hidden;
  }
  .embla__container {
    display: flex;
  }
  .embla__slide {
    flex: 0 0 auto;
    min-width: 0;
	scroll-snap-align: none;  
    will-change: transform;  
  }
`;

const topRowFeatures = [
	{
		icon: scheduler,
		title: "Scheduler",
		desc: "Enable online appointment booking with automated reminders.",
	},
	{
		icon: planner,
		title: "Planner",
		desc: "Set provider availability, preferences, and service rules to match your real-world workflows.",
	},
	{
		icon: forms,
		title: "Forms & Intake",
		desc: "Create digital intake forms with fully customizable fields. Eliminate paper and manual entry.",
	},
	{
		icon: analytics,
		title: "Analytics",
		desc: "Access real-time insights on provider availability, patient demand, and utilization trends.",
	},
	{
		icon: messaging,
		title: "Secure Messaging",
		desc: "Send HIPAA-compliant reminders and updates automatically via email or SMS.",
	},
	{
		icon: referals,
		title: "Referrals",
		desc: "Easily track and manage patient referrals for smooth, coordinated care.",
	},
];

const bottomRowFeatures = [
	{
		icon: auto,
		title: "Automation Engine",
		desc: "Automate workflows and routine tasks like sending forms, follow-ups, and cancellations with ease.",
	},
	{
		icon: ai,
		title: "Kemi (AI Copilot)",
		desc: "Complete complex admin tasks instantly using simple prompts. Just ask, and it's done.",
	},
	{
		icon: auto,
		title: "EMR Task Automation",
		desc: "Use AI to automate routine EMR tasks and improve workflow efficiency.",
	},
	{
		icon: inventory,
		title: "Inventory",
		desc: "Monitor inventory levels with automated alerts to reduce waste and ensure availability.",
	},
	{
		icon: assets,
		title: "Asset Management",
		desc: "Simplify monitoring, maintenance, and compliance tracking to keep assets secure and up to date.",
	},
	{
		icon: market,
		title: "Marketplace & Integrations",
		desc: "Seamlessly integrate with EMRs, EHRs, and your existing tools for a unified workflow.",
	},
];

export const Testing = () => {
	const isMd = useMediaQuery("(min-width: 600px)");
	const isLg = useMediaQuery("(min-width: 960px)");
	const isXl = useMediaQuery("(min-width: 1280px)");

	const getCardWidth = useCallback(() => {
		if (typeof window === "undefined") return 390;
		if (!isMd) return window.innerWidth - 32;
		if (!isLg) return 450;
		if (!isXl) return 320;
		return 390;
	}, [isMd, isLg, isXl]);

	const [CARD_WIDTH, setCardWidth] = useState(390);
	const GAP = 8;

	useEffect(() => {
		const styleElement = document.createElement("style");
		styleElement.textContent = emblaStyles;
		document.head.appendChild(styleElement);

		return () => {
			if (document.head.contains(styleElement)) {
				document.head.removeChild(styleElement);
			}
		};
	}, []);

	useEffect(() => {
		setCardWidth(getCardWidth());

		const handleResize = () => {
			setCardWidth(getCardWidth());
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, [isMd, isLg, isXl, getCardWidth]);

	const [topCardIndex, setTopCardIndex] = useState(0);
	const [bottomCardIndex, setBottomCardIndex] = useState(0);
	const [isUserInteracting, setIsUserInteracting] = useState(false);

	const [topEmblaRef, topEmblaApi] = useEmblaCarousel({
		loop: true,
		align: "start",
		skipSnaps: false,
		dragFree: true,
		containScroll: "trimSnaps",
		duration: 10,
		startIndex: 0,
	});

	const [bottomEmblaRef, bottomEmblaApi] = useEmblaCarousel({
		loop: true,
		align: "start",
		skipSnaps: false,
		dragFree: true,
		containScroll: "trimSnaps",
		duration: 10,
		startIndex: 0,
	});

	const manualInteractionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const smoothScrollAnimationRef = useRef<number | null>(null);
	const lastScrollTimeRef = useRef<number>(0);
	const scrollProgressRef = useRef<number>(0);

	const smoothAutoScroll = useCallback(
		(timestamp: number) => {
			if (!topEmblaApi || !bottomEmblaApi || !isMd || isUserInteracting) {
				smoothScrollAnimationRef.current =
					requestAnimationFrame(smoothAutoScroll);
				return;
			}

			if (lastScrollTimeRef.current === 0) {
				lastScrollTimeRef.current = timestamp;
			}

			const deltaTime = timestamp - lastScrollTimeRef.current;
			lastScrollTimeRef.current = timestamp;

			const scrollSpeed = 0.0003;
			scrollProgressRef.current += deltaTime * scrollSpeed;
			if (scrollProgressRef.current >= 1) {
				scrollProgressRef.current = 0;
				topEmblaApi.scrollNext();
				bottomEmblaApi.scrollPrev();
			}

			smoothScrollAnimationRef.current =
				requestAnimationFrame(smoothAutoScroll);
		},
		[topEmblaApi, bottomEmblaApi, isMd, isUserInteracting]
	);

	const startAutoScroll = useCallback(() => {
		if (!topEmblaApi || !bottomEmblaApi || !isMd) return;
		if (smoothScrollAnimationRef.current) {
			cancelAnimationFrame(smoothScrollAnimationRef.current);
		}
		scrollProgressRef.current = 0;
		lastScrollTimeRef.current = 0;
		smoothScrollAnimationRef.current =
			requestAnimationFrame(smoothAutoScroll);
	}, [topEmblaApi, bottomEmblaApi, isMd, smoothAutoScroll]);

	const stopAutoScroll = useCallback(() => {
		if (smoothScrollAnimationRef.current) {
			cancelAnimationFrame(smoothScrollAnimationRef.current);
			smoothScrollAnimationRef.current = null;
		}
		lastScrollTimeRef.current = 0;
		scrollProgressRef.current = 0;
	}, []);

	useEffect(() => {
		if (topEmblaApi && bottomEmblaApi && isMd && !isUserInteracting) {
			startAutoScroll();
		} else {
			stopAutoScroll();
		}

		return () => {
			stopAutoScroll();
			if (manualInteractionTimeoutRef.current) {
				clearTimeout(manualInteractionTimeoutRef.current);
			}
		};
	}, [
		topEmblaApi,
		bottomEmblaApi,
		isMd,
		isUserInteracting,
		startAutoScroll,
		stopAutoScroll,
	]);

	const handleMouseEnter = useCallback(() => {
		if (isMd) {
			setIsUserInteracting(true);
		}
	}, [isMd]);

	const handleMouseLeave = useCallback(() => {
		if (isMd) {
			setIsUserInteracting(false);
		}
	}, [isMd]);
	const handleNext = useCallback(() => {
		if (!isMd) {
			if (topCardIndex < topRowFeatures.length - 1) {
				setTopCardIndex((prevIndex) => prevIndex + 1);
			} else {
				setTopCardIndex(0);
			}
			if (bottomCardIndex < bottomRowFeatures.length - 1) {
				setBottomCardIndex((prevIndex) => prevIndex + 1);
			} else {
				setBottomCardIndex(0);
			}
			return;
		}
		if (topEmblaApi && bottomEmblaApi) {
			setIsUserInteracting(true);
			scrollProgressRef.current = 0;
			lastScrollTimeRef.current = 0;
			topEmblaApi.scrollNext();
			bottomEmblaApi.scrollPrev();
			if (manualInteractionTimeoutRef.current) {
				clearTimeout(manualInteractionTimeoutRef.current);
			}

			manualInteractionTimeoutRef.current = setTimeout(() => {
				setIsUserInteracting(false);
			}, 3000);
		}
	}, [isMd, topEmblaApi, bottomEmblaApi, topCardIndex, bottomCardIndex]);

	const handlePrev = useCallback(() => {
		if (!isMd) {
			if (topCardIndex > 0) {
				setTopCardIndex((prevIndex) => prevIndex - 1);
			} else {
				setTopCardIndex(topRowFeatures.length - 1);
			}
			if (bottomCardIndex > 0) {
				setBottomCardIndex((prevIndex) => prevIndex - 1);
			} else {
				setBottomCardIndex(bottomRowFeatures.length - 1);
			}
			return;
		}
		if (topEmblaApi && bottomEmblaApi) {
			setIsUserInteracting(true);
			scrollProgressRef.current = 0;
			lastScrollTimeRef.current = 0;
			topEmblaApi.scrollPrev();
			bottomEmblaApi.scrollNext();
			if (manualInteractionTimeoutRef.current) {
				clearTimeout(manualInteractionTimeoutRef.current);
			}
			manualInteractionTimeoutRef.current = setTimeout(() => {
				setIsUserInteracting(false);
			}, 3000);
		}
	}, [isMd, topEmblaApi, bottomEmblaApi, topCardIndex, bottomCardIndex]);
	const isPreviousDisabled = !isMd && topCardIndex === 0;
	const isNextDisabled = !isMd && topCardIndex === topRowFeatures.length - 1;
	const hasNextCard = topCardIndex + 1 < topRowFeatures.length;
	const hasPrevCard = topCardIndex - 1 >= 0;

	return (
		<section
			className="mt-[38px] flex w-full flex-col items-center justify-center px-4 py-[52px] font-inter lg:mt-4 lg:overflow-hidden lg:py-[96px]"
			id="features"
		>
			<div className="mx-auto flex w-full max-w-[312px] flex-col items-center text-center sm:max-w-6xl">
				<span className="mb-2 !font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
					Features
				</span>
				<h2 className="mb-2 text-lg font-bold text-[#0C0D0E] lg:text-[32px]">
					All the tools you need, in one platform
				</h2>
				<p className="mb-6 max-w-[648px] text-sm text-[#68778D] sm:mb-10 lg:text-lg">
					No need to juggle multiple apps. Every essential tool you
					need to operate efficiently is built into one simple,
					intuitive solution.
				</p>
			</div>

			{!isMd && (
				<div className="w-screen">
					<div className="relative overflow-hidden">
						<div className="flex snap-x snap-mandatory justify-center gap-2 overflow-x-hidden">
							<div
								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${hasPrevCard ? "" : "border-transparent"}`}
							>
								<div className="flex gap-3">
									<img
										src={
											topRowFeatures[topCardIndex - 1]
												?.icon?.src ||
											topRowFeatures[0].icon.src
										}
										alt={
											topRowFeatures[topCardIndex - 1]
												?.title
										}
										className="h-8 w-8"
									/>
									<div>
										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
											{
												topRowFeatures[topCardIndex - 1]
													?.title
											}
										</h3>
										<p className="text-sm text-[#303741]">
											{
												topRowFeatures[topCardIndex - 1]
													?.desc
											}
										</p>
									</div>
								</div>
							</div>
							<div className="duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out">
								<div className="flex gap-3">
									<img
										src={
											topRowFeatures[topCardIndex]?.icon
												?.src
										}
										alt={
											topRowFeatures[topCardIndex]?.title
										}
										className="h-8 w-8"
									/>
									<div>
										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
											{
												topRowFeatures[topCardIndex]
													?.title
											}
										</h3>
										<p className="text-sm text-[#303741]">
											{topRowFeatures[topCardIndex]?.desc}
										</p>
									</div>
								</div>
							</div>
							<div
								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${!hasNextCard ? "border-transparent" : ""}`}
							>
								{hasNextCard && (
									<div className={`flex gap-3`}>
										<img
											src={
												topRowFeatures[topCardIndex + 1]
													?.icon?.src
											}
											alt={
												topRowFeatures[topCardIndex + 1]
													?.title
											}
											className="h-8 w-8"
										/>
										<div>
											<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
												{
													topRowFeatures[
														topCardIndex + 1
													]?.title
												}
											</h3>
											<p className="text-sm text-[#303741]">
												{
													topRowFeatures[
														topCardIndex + 1
													]?.desc
												}
											</p>
										</div>
									</div>
								)}
							</div>
						</div>
						<div className="mt-6 flex snap-x snap-mandatory justify-center gap-2 overflow-x-hidden">
							<div
								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${hasPrevCard ? "" : "border-transparent"}`}
							>
								<div className="flex gap-3">
									<img
										src={
											bottomRowFeatures[
												bottomCardIndex - 1
											]?.icon?.src ||
											bottomRowFeatures[0].icon.src
										}
										alt={
											bottomRowFeatures[
												bottomCardIndex - 1
											]?.title
										}
										className="h-8 w-8"
									/>
									<div>
										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
											{
												bottomRowFeatures[
													bottomCardIndex - 1
												]?.title
											}
										</h3>
										<p className="text-sm text-[#303741]">
											{
												bottomRowFeatures[
													bottomCardIndex - 1
												]?.desc
											}
										</p>
									</div>
								</div>
							</div>
							<div className="duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out">
								<div className="flex gap-3">
									<img
										src={
											bottomRowFeatures[bottomCardIndex]
												?.icon?.src
										}
										alt={
											bottomRowFeatures[bottomCardIndex]
												?.title
										}
										className="h-8 w-8"
									/>
									<div>
										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
											{
												bottomRowFeatures[
													bottomCardIndex
												]?.title
											}
										</h3>
										<p className="text-sm text-[#303741]">
											{
												bottomRowFeatures[
													bottomCardIndex
												]?.desc
											}
										</p>
									</div>
								</div>
							</div>
							<div
								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${!hasNextCard ? "border-transparent" : ""}`}
							>
								{hasNextCard && (
									<div className={`flex gap-3`}>
										<img
											src={
												bottomRowFeatures[
													bottomCardIndex + 1
												]?.icon?.src
											}
											alt={
												bottomRowFeatures[
													bottomCardIndex + 1
												]?.title
											}
											className="h-8 w-8"
										/>
										<div>
											<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
												{
													bottomRowFeatures[
														bottomCardIndex + 1
													]?.title
												}
											</h3>
											<p className="text-sm text-[#303741]">
												{
													bottomRowFeatures[
														bottomCardIndex + 1
													]?.desc
												}
											</p>
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			)}
			{isMd && (
				<div
					className="w-full max-w-6xl"
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
					onTouchStart={handleMouseEnter}
					onTouchEnd={handleMouseLeave}
				>
					{/* Top Carousel - Scrolls Left */}
					<div className="mb-4 w-full overflow-hidden sm:mb-6">
						<div className="embla" ref={topEmblaRef}>
							<div
								className="embla__container flex"
								style={{ gap: GAP }}
							>
								{topRowFeatures.map((feature, idx) => (
									<div
										key={`top-embla-${idx}`}
										className="embla__slide flex-none"
										style={{
											width: CARD_WIDTH,
											marginRight: GAP,
										}}
									>
										<div className="h-full min-h-[168px] rounded-xl border border-[#E4E4E7] bg-white p-4 shadow-[0px_1px_2px_0px_#0000000D] transition hover:shadow-md sm:mx-2 sm:p-6">
											<div className="flex gap-3">
												<img
													src={feature.icon.src}
													alt={feature.title}
													className="h-6 w-6 sm:h-7 sm:w-7"
												/>
												<div>
													<h3 className="mb-1 text-lg font-semibold text-[#0C0D0E] antialiased">
														{feature.title}
													</h3>
													<p className="text-base font-normal text-[#303741] antialiased">
														{feature.desc}
													</p>
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Bottom Carousel - Scrolls Right */}
					<div className="w-full overflow-hidden">
						<div className="embla" ref={bottomEmblaRef}>
							<div
								className="embla__container flex"
								style={{ gap: GAP }}
							>
								{bottomRowFeatures.map((feature, idx) => (
									<div
										key={`bottom-embla-${idx}`}
										className="embla__slide flex-none"
										style={{
											width: CARD_WIDTH,
											marginRight: GAP,
										}}
									>
										<div className="h-full min-h-[168px] rounded-xl border border-[#E4E4E7] bg-white p-4 shadow-[0px_1px_2px_0px_#0000000D] transition hover:shadow-md sm:mx-2 sm:p-6">
											<div className="flex gap-3">
												<img
													src={feature.icon.src}
													alt={feature.title}
													className="h-6 w-6 sm:h-7 sm:w-7"
												/>
												<div>
													<h3 className="mb-1 text-lg font-semibold text-[#0C0D0E] antialiased">
														{feature.title}
													</h3>
													<p className="text-base font-normal text-[#303741] antialiased">
														{feature.desc}
													</p>
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			)}
			<div className="mt-6 flex justify-center gap-12 sm:mt-10">
				<button
					onClick={handlePrev}
					disabled={isPreviousDisabled}
					className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E8EBEE] text-[#0C0D0E] shadow-sm hover:text-[#0C0D0E]/60"
					aria-label="Previous"
				>
					<ArrowLeft className="h-4 w-4" />
				</button>
				<button
					onClick={handleNext}
					disabled={isNextDisabled}
					className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E8EBEE] text-[#0C0D0E] shadow-sm hover:text-[#0C0D0E]/60"
					aria-label="Next"
				>
					<ArrowRight className="h-4 w-4" />
				</button>
			</div>
		</section>
	);
};

export function useMediaQuery(query: string): boolean {
	const [matches, setMatches] = useState(false);

	useEffect(() => {
		const media = window.matchMedia(query);
		if (media.matches !== matches) {
			setMatches(media.matches);
		}
		const listener = () => setMatches(media.matches);
		media.addEventListener("change", listener);
		return () => media.removeEventListener("change", listener);
	}, [matches, query]);

	return matches;
}

// export const Features = () => {
// 	const isMd = useMediaQuery("(min-width: 600px)");
// 	const isLg = useMediaQuery("(min-width: 960px)");
// 	const isXl = useMediaQuery("(min-width: 1280px)");

// 	const getCardWidth = () => {
// 		if (!isMd) return window.innerWidth - 32;
// 		if (!isLg) return 450;
// 		if (!isXl) return 320;
// 		return 390;
// 	};

// 	const [CARD_WIDTH, setCardWidth] = useState(390);
// 	const ANIMATION_DURATION = 150;

// 	useEffect(() => {
// 		setCardWidth(getCardWidth());

// 		const handleResize = () => {
// 			setCardWidth(getCardWidth());
// 		};

// 		window.addEventListener("resize", handleResize);
// 		return () => window.removeEventListener("resize", handleResize);
// 	}, [isMd, isLg, isXl]);

// 	const topRowItems = [...topRowFeatures, ...topRowFeatures];
// 	const bottomRowItems = [...bottomRowFeatures, ...bottomRowFeatures];

// 	const totalWidth = CARD_WIDTH * topRowFeatures.length;
// 	const topRowControls = useAnimationControls();
// 	const bottomRowControls = useAnimationControls();
// 	const [isUserInteracting, setIsUserInteracting] = useState(false);
// 	const [topRowPosition, setTopRowPosition] = useState(0);
// 	const [bottomRowPosition, setBottomRowPosition] = useState(-totalWidth);
// 	const [topCardIndex, setTopCardIndex] = useState(0);
// 	const [bottomCardIndex, setBottomCardIndex] = useState(0);
// 	const inactivityTimerRef = useRef<ReturnType<typeof setTimeout> | null>(
// 		null
// 	);
// 	const startContinuousAnimation = () => {
// 		if (isUserInteracting || !isMd) return;
// 		setTopRowPosition(0);
// 		setBottomRowPosition(-totalWidth);

// 		topRowControls.start({
// 			x: [0, -totalWidth],
// 			transition: {
// 				repeat: Infinity,
// 				repeatType: "loop",
// 				duration: ANIMATION_DURATION,
// 				ease: "linear",
// 			},
// 		});

// 		bottomRowControls.start({
// 			x: [-totalWidth, 0],
// 			transition: {
// 				repeat: Infinity,
// 				repeatType: "loop",
// 				duration: ANIMATION_DURATION,
// 				ease: "linear",
// 			},
// 		});
// 	};

// 	useEffect(() => {
// 		if (isMd && !isUserInteracting) {
// 			startContinuousAnimation();
// 		}
// 	}, [isUserInteracting, totalWidth, isMd]);
// 	useEffect(() => {
// 		return () => {
// 			if (inactivityTimerRef.current) {
// 				clearTimeout(inactivityTimerRef.current);
// 			}
// 		};
// 	}, []);

// 	// const handleMouseEnter = () => {
// 	// 	if (isMd) {
// 	// 		setIsUserInteracting(true);
// 	// 		topRowControls.stop();
// 	// 		bottomRowControls.stop();
// 	// 	}
// 	// };

// 	// const handleMouseLeave = () => {
// 	// 	if (isMd) {
// 	// 		setIsUserInteracting(false);
// 	// 	}
// 	// };
// 	const handleUserInteraction = () => {
// 		setIsUserInteracting(true);
// 		if (inactivityTimerRef.current) {
// 			clearTimeout(inactivityTimerRef.current);
// 		}
// 		inactivityTimerRef.current = setTimeout(() => {
// 			setIsUserInteracting(false);
// 		}, 8000);
// 	};
// 	const handleNext = () => {
// 		handleUserInteraction();
// 		if (!isMd) {
// 			if (topCardIndex < topRowFeatures.length - 1) {
// 				setTopCardIndex((prevIndex) => prevIndex + 1);
// 			} else {
// 				setTopCardIndex(0);
// 			}
// 			if (bottomCardIndex < bottomRowFeatures.length - 1) {
// 				setBottomCardIndex((prevIndex) => prevIndex + 1);
// 			} else {
// 				setBottomCardIndex(0);
// 			}
// 			return;
// 		}
// 		const newTopPosition = topRowPosition - CARD_WIDTH;
// 		const newBottomPosition = bottomRowPosition + CARD_WIDTH;
// 		if (newTopPosition <= -totalWidth) {
// 			topRowControls.set({ x: newTopPosition + totalWidth });
// 			setTopRowPosition(newTopPosition + totalWidth);
// 			topRowControls.start({
// 				x: newTopPosition + totalWidth,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 		} else {
// 			topRowControls.start({
// 				x: newTopPosition,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 			setTopRowPosition(newTopPosition);
// 		}
// 		if (newBottomPosition >= 0) {
// 			bottomRowControls.set({ x: newBottomPosition - totalWidth });
// 			setBottomRowPosition(newBottomPosition - totalWidth);
// 			bottomRowControls.start({
// 				x: newBottomPosition - totalWidth,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 		} else {
// 			bottomRowControls.start({
// 				x: newBottomPosition,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 			setBottomRowPosition(newBottomPosition);
// 		}
// 	};

// 	const handlePrev = () => {
// 		handleUserInteraction();
// 		if (!isMd) {
// 			if (topCardIndex > 0) {
// 				setTopCardIndex((prevIndex) => prevIndex - 1);
// 			} else {
// 				setTopCardIndex(topRowFeatures.length - 1);
// 			}
// 			if (bottomCardIndex > 0) {
// 				setBottomCardIndex((prevIndex) => prevIndex - 1);
// 			} else {
// 				setBottomCardIndex(bottomRowFeatures.length - 1);
// 			}
// 			return;
// 		}

// 		const newTopPosition = topRowPosition + CARD_WIDTH;
// 		const newBottomPosition = bottomRowPosition - CARD_WIDTH;

// 		if (newTopPosition > 0) {
// 			topRowControls.set({ x: newTopPosition - totalWidth });
// 			setTopRowPosition(newTopPosition - totalWidth);

// 			topRowControls.start({
// 				x: newTopPosition - totalWidth,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 		} else {
// 			topRowControls.start({
// 				x: newTopPosition,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 			setTopRowPosition(newTopPosition);
// 		}

// 		if (newBottomPosition < -totalWidth) {
// 			bottomRowControls.set({ x: newBottomPosition + totalWidth });
// 			setBottomRowPosition(newBottomPosition + totalWidth);

// 			bottomRowControls.start({
// 				x: newBottomPosition + totalWidth,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 		} else {
// 			bottomRowControls.start({
// 				x: newBottomPosition,
// 				transition: { duration: 0.95, ease: "easeInOut" },
// 			});
// 			setBottomRowPosition(newBottomPosition);
// 		}
// 	};
// 	const isPreviousDisabled = !isMd && topCardIndex === 0;
// 	const isNextDisabled = !isMd && topCardIndex === topRowFeatures.length - 1;
// 	const hasNextCard = topCardIndex + 1 < topRowFeatures.length;
// 	const hasPrevCard = topCardIndex - 1 >= 0;

// 	return (
// 		<section
// 			className="mt-[38px] flex w-full flex-col items-center justify-center px-4 py-[52px] font-inter lg:mt-4 lg:overflow-hidden lg:py-[96px]"
// 			id="features"
// 		>
// 			<div className="mx-auto flex w-full max-w-[312px] flex-col items-center text-center sm:max-w-6xl">
// 				<span className="mb-2 !font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
// 					Features
// 				</span>
// 				<h2 className="mb-2 text-lg font-bold text-[#0C0D0E] lg:text-[32px]">
// 					All the tools you need, in one platform
// 				</h2>
// 				<p className="mb-6 max-w-[648px] text-sm text-[#68778D] sm:mb-10 lg:text-lg">
// 					No need to juggle multiple apps. Every essential tool you
// 					need to operate efficiently is built into one simple,
// 					intuitive solution.
// 				</p>
// 			</div>

// 			{!isMd && (
// 				<div className="w-screen">
// 					<div className="relative overflow-hidden">
// 						<div className="flex snap-x snap-mandatory justify-center gap-2 overflow-x-hidden">
// 							<div
// 								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${hasPrevCard ? "" : "border-transparent"}`}
// 							>
// 								<div className="flex gap-3">
// 									<img
// 										src={
// 											topRowFeatures[topCardIndex - 1]
// 												?.icon?.src ||
// 											topRowFeatures[0].icon.src
// 										}
// 										alt={
// 											topRowFeatures[topCardIndex - 1]
// 												?.title
// 										}
// 										className="h-8 w-8"
// 									/>
// 									<div>
// 										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 											{
// 												topRowFeatures[topCardIndex - 1]
// 													?.title
// 											}
// 										</h3>
// 										<p className="text-sm text-[#303741]">
// 											{
// 												topRowFeatures[topCardIndex - 1]
// 													?.desc
// 											}
// 										</p>
// 									</div>
// 								</div>
// 							</div>
// 							<div className="duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out">
// 								<div className="flex gap-3">
// 									<img
// 										src={
// 											topRowFeatures[topCardIndex]?.icon
// 												?.src
// 										}
// 										alt={
// 											topRowFeatures[topCardIndex]?.title
// 										}
// 										className="h-8 w-8"
// 									/>
// 									<div>
// 										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 											{
// 												topRowFeatures[topCardIndex]
// 													?.title
// 											}
// 										</h3>
// 										<p className="text-sm text-[#303741]">
// 											{topRowFeatures[topCardIndex]?.desc}
// 										</p>
// 									</div>
// 								</div>
// 							</div>
// 							<div
// 								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${!hasNextCard ? "border-transparent" : ""}`}
// 							>
// 								{hasNextCard && (
// 									<div className={`flex gap-3`}>
// 										<img
// 											src={
// 												topRowFeatures[topCardIndex + 1]
// 													?.icon?.src
// 											}
// 											alt={
// 												topRowFeatures[topCardIndex + 1]
// 													?.title
// 											}
// 											className="h-8 w-8"
// 										/>
// 										<div>
// 											<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 												{
// 													topRowFeatures[
// 														topCardIndex + 1
// 													]?.title
// 												}
// 											</h3>
// 											<p className="text-sm text-[#303741]">
// 												{
// 													topRowFeatures[
// 														topCardIndex + 1
// 													]?.desc
// 												}
// 											</p>
// 										</div>
// 									</div>
// 								)}
// 							</div>
// 						</div>
// 						<div className="mt-6 flex snap-x snap-mandatory justify-center gap-2 overflow-x-hidden">
// 							<div
// 								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${hasPrevCard ? "" : "border-transparent"}`}
// 							>
// 								<div className="flex gap-3">
// 									<img
// 										src={
// 											bottomRowFeatures[
// 												bottomCardIndex - 1
// 											]?.icon?.src ||
// 											bottomRowFeatures[0].icon.src
// 										}
// 										alt={
// 											bottomRowFeatures[
// 												bottomCardIndex - 1
// 											]?.title
// 										}
// 										className="h-8 w-8"
// 									/>
// 									<div>
// 										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 											{
// 												bottomRowFeatures[
// 													bottomCardIndex - 1
// 												]?.title
// 											}
// 										</h3>
// 										<p className="text-sm text-[#303741]">
// 											{
// 												bottomRowFeatures[
// 													bottomCardIndex - 1
// 												]?.desc
// 											}
// 										</p>
// 									</div>
// 								</div>
// 							</div>
// 							<div className="duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out">
// 								<div className="flex gap-3">
// 									<img
// 										src={
// 											bottomRowFeatures[bottomCardIndex]
// 												?.icon?.src
// 										}
// 										alt={
// 											bottomRowFeatures[bottomCardIndex]
// 												?.title
// 										}
// 										className="h-8 w-8"
// 									/>
// 									<div>
// 										<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 											{
// 												bottomRowFeatures[
// 													bottomCardIndex
// 												]?.title
// 											}
// 										</h3>
// 										<p className="text-sm text-[#303741]">
// 											{
// 												bottomRowFeatures[
// 													bottomCardIndex
// 												]?.desc
// 											}
// 										</p>
// 									</div>
// 								</div>
// 							</div>
// 							<div
// 								className={`duration-600 w-[90vw] flex-shrink-0 snap-center rounded-xl border border-[#E5E7EB] p-4 px-2 transition-all ease-in-out ${!hasNextCard ? "border-transparent" : ""}`}
// 							>
// 								{hasNextCard && (
// 									<div className={`flex gap-3`}>
// 										<img
// 											src={
// 												bottomRowFeatures[
// 													bottomCardIndex + 1
// 												]?.icon?.src
// 											}
// 											alt={
// 												bottomRowFeatures[
// 													bottomCardIndex + 1
// 												]?.title
// 											}
// 											className="h-8 w-8"
// 										/>
// 										<div>
// 											<h3 className="mb-1 text-base font-semibold text-[#0C0D0E]">
// 												{
// 													bottomRowFeatures[
// 														bottomCardIndex + 1
// 													]?.title
// 												}
// 											</h3>
// 											<p className="text-sm text-[#303741]">
// 												{
// 													bottomRowFeatures[
// 														bottomCardIndex + 1
// 													]?.desc
// 												}
// 											</p>
// 										</div>
// 									</div>
// 								)}
// 							</div>
// 						</div>
// 					</div>
// 				</div>
// 			)}

// 			{isMd && (
// 				<div
// 					className={`w-full max-w-6xl`}
// 					// onMouseEnter={handleMouseEnter}
// 					// onMouseLeave={handleMouseLeave}
// 					// onTouchStart={handleMouseEnter}
// 					// onTouchEnd={handleMouseLeave}
// 				>
// 					<div className="mb-4 w-full overflow-hidden sm:mb-6">
// 						<motion.div
// 							className="flex"
// 							animate={topRowControls}
// 							initial={{ x: topRowPosition }}
// 						>
// 							{topRowItems.map((feature, idx) => (
// 								<div
// 									key={`top-${idx}`}
// 									className="flex-none"
// 									style={{ width: CARD_WIDTH }}
// 								>
// 									<div className="mx-1 h-full min-h-[168px] rounded-xl border border-[#E4E4E7] bg-white p-4 shadow-[0px_1px_2px_0px_#0000000D] transition sm:mx-3 sm:p-6">
// 										<div className="flex gap-3">
// 											<img
// 												src={feature.icon.src}
// 												alt={feature.title}
// 												className="h-6 w-6 sm:h-7 sm:w-7"
// 											/>
// 											<div>
// 												<h3 className="mb-1 text-lg font-semibold text-[#0C0D0E] antialiased">
// 													{feature.title}
// 												</h3>
// 												<p className="text-base font-normal text-[#303741] antialiased">
// 													{feature.desc}
// 												</p>
// 											</div>
// 										</div>
// 									</div>
// 								</div>
// 							))}
// 						</motion.div>
// 					</div>

// 					<div className="w-full overflow-hidden">
// 						<motion.div
// 							className="flex"
// 							animate={bottomRowControls}
// 							initial={{ x: bottomRowPosition }}
// 						>
// 							{bottomRowItems.map((feature, idx) => (
// 								<div
// 									key={`bottom-${idx}`}
// 									className="flex-none"
// 									style={{ width: CARD_WIDTH }}
// 								>
// 									<div className="mx-1 h-full min-h-[168px] rounded-xl border border-[#E4E4E7] bg-white p-4 shadow-[0px_1px_2px_0px_#0000000D] transition sm:mx-3 sm:p-6">
// 										<div className="flex gap-3">
// 											<img
// 												src={feature.icon.src}
// 												alt={feature.title}
// 												className="h-6 w-6 sm:h-7 sm:w-7"
// 											/>
// 											<div>
// 												<h3 className="mb-1 text-lg font-semibold text-[#0C0D0E] antialiased">
// 													{feature.title}
// 												</h3>
// 												<p className="text-base font-normal text-[#303741] antialiased">
// 													{feature.desc}
// 												</p>
// 											</div>
// 										</div>
// 									</div>
// 								</div>
// 							))}
// 						</motion.div>
// 					</div>
// 				</div>
// 			)}

// 			<div className="mt-6 flex justify-center gap-12 sm:mt-10">
// 				<button
// 					onClick={handlePrev}
// 					disabled={isPreviousDisabled}
// 					className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E8EBEE] text-[#0C0D0E] shadow-sm hover:text-[#0C0D0E]/60"
// 					aria-label="Previous"
// 				>
// 					<ArrowLeft className="h-4 w-4" />
// 				</button>
// 				<button
// 					onClick={handleNext}
// 					disabled={isNextDisabled}
// 					className="flex h-10 w-10 items-center justify-center rounded-full bg-[#E8EBEE] text-[#0C0D0E] shadow-sm hover:text-[#0C0D0E]/60"
// 					aria-label="Next"
// 				>
// 					<ArrowRight className="h-4 w-4" />
// 				</button>
// 			</div>
// 		</section>
// 	);
// };

// export function useMediaQuery(query: string): boolean {
// 	const [matches, setMatches] = useState(false);

// 	useEffect(() => {
// 		const media = window.matchMedia(query);
// 		if (media.matches !== matches) {
// 			setMatches(media.matches);
// 		}

// 		const listener = () => setMatches(media.matches);
// 		media.addEventListener("change", listener);

// 		return () => media.removeEventListener("change", listener);
// 	}, [matches, query]);

// 	return matches;
// }