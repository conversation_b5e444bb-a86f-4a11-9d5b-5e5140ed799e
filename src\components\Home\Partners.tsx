import React from "react";

export const Partners = () => {
	const partnerLogos = [
		{ src: "/assets/images/OH-logo.svg", alt: "Ontario Health" },
		{
			src: "/assets/images/UOW.svg",
			alt: "University of Waterloo",
		},
		{
			src: "/assets/images/NM.svg",
			alt: "Network Medicals",
		},
		{
			src: "/assets/images/NWT.svg",
			alt: "North Western Toronto",
		},
		{ src: "/assets/images/microsoft.svg", alt: "Microsoft" },
		{ src: "/assets/images/velocity.png", alt: "Velocity" },
	];

	return (
		<section className="w-full py-[52px] sm:mt-[30px] lg:py-12 xl:mt-10">
			<div className="hidden lg:flex lg:items-center lg:justify-center">
				<div className="flex flex-wrap items-center justify-center gap-[52.96px]">
					{partnerLogos.map((logo, idx) => (
						<img
							key={idx}
							src={logo.src}
							alt={logo.alt}
							className="h-8 w-auto flex-shrink-0 grayscale transition-all duration-300 hover:grayscale-0"
							style={{ maxWidth: 140 }}
						/>
					))}
				</div>
			</div>

			{/*Mobile*/}
			<div className="overflow-hidden lg:hidden">
				<div
					className="flex gap-[52.96px]"
					style={{
						animation: "scroll 45s linear infinite",
						width: "max-content",
					}}
				>
					{[...partnerLogos, ...partnerLogos].map((logo, idx) => (
						<div key={idx} className="flex-shrink-0">
							<img
								src={logo.src}
								alt={logo.alt}
								className="h-8 w-auto grayscale"
								style={{ maxWidth: 140 }}
							/>
						</div>
					))}
				</div>
			</div>

			<style>{`
				@keyframes scroll {
					0% {
						transform: translateX(0);
					}
					100% {
						transform: translateX(-50%);
					}
				}
			`}</style>
		</section>
	);
};
